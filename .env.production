# 生产环境
VITE_USER_NODE_ENV = production

# 路由模式 hash | history
VITE_ROUTER_MODE = hash

# 打包路径 开发环境地址前缀 (一般 '/' 或 './' 都可以，如果开发环境 '/' 打包预览白屏，请使用 './')
# 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上，所以这里 VITE_PUBLIC_PATH 为 '/'
# 有时候需要使用相对路径'./'，例如你要打包electron的时候，就需要使用相对路径'./'找到对应资源文件
# 打包路径 (就是网站前缀, 例如 http://SnowAdmin.gitee.io/ 如果应用被部署在一个子路径上,你就需要用这个选项指定这个子路径,如果部署到 http://SnowAdmin.gitee.io/SnowAdmin/ 域名下, VITE_PUBLIC_PATH就需要填写 /SnowAdmin/)
VITE_PUBLIC_PATH = '/'

# 请求路径 管理系统/开发环境
VITE_APP_BASE_URL = '/'
