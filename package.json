{"name": "snow-admin", "private": true, "version": "1.0.0", "type": "module", "description": "snow-admin open source management system", "author": {"name": "WANGFan", "email": "<EMAIL>", "url": "https://github.com/WANG-Fan0912"}, "license": "MIT", "homepage": "https://github.com/WANG-Fan0912/SnowAdmin", "repository": {"type": "git", "url": "https://github.com/WANG-Fan0912/SnowAdmin.git"}, "bugs": {"url": "https://github.com/WANG-Fan0912/SnowAdmin/issues"}, "scripts": {"dev": "vite", "build:dev": "vue-tsc && vite build --mode development", "build:prod": "vue-tsc && vite build --mode production", "build:test": "vue-tsc && vite build --mode test", "preview": "vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:lint-staged": "lint-staged", "fix": "eslint src --fix", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@arco-design/color": "^0.4.0", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-vue": "^0.1.3", "@codemirror/theme-one-dark": "^6.1.2", "@fingerprintjs/fingerprintjs": "^4.6.2", "@visactor/vchart": "^1.11.0", "@visactor/vchart-arco-theme": "^1.11.0", "@vueuse/core": "^12.4.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.8", "codemirror": "^6.0.1", "driver.js": "^1.3.1", "fingerprintjs2": "^2.1.4", "jsbarcode": "^3.11.6", "nprogress": "^0.2.0", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^3.2.1", "pinyin-pro": "^3.26.0", "print-js": "^1.6.0", "qrcode": "^1.5.4", "recorder-core": "^1.3.25011100", "sortablejs": "^1.15.2", "uuid": "^11.1.0", "vue": "^3.5.15", "vue-codemirror6": "^1.3.0", "vue-color-kit": "^1.0.6", "vue-i18n": "10.0.0-alpha.3", "vue-pick-colors": "^1.7.6", "vue-router": "^4.3.0", "vue-virtual-scroller": "2.0.0-beta.8", "vuedraggable": "^4.1.0", "xgplayer": "^3.0.18"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@arco-plugins/vite-vue": "^1.4.5", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/node": "^22.13.11", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-vue": "^5.2.1", "commitlint": "^19.2.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^10.0.0", "globals": "^16.1.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "postcss": "^8.4.38", "postcss-html": "^1.6.0", "postcss-preset-env": "^9.5.2", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "sass-embedded": "^1.86.0", "stylelint": "^16.16.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^37.0.0", "stylelint-config-standard-scss": "^14.0.0", "terser": "^5.29.2", "typescript": "^5.2.2", "typescript-eslint": "^8.32.1", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mock": "^2.9.6", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.4"}, "engines": {"node": ">= 18.12.0", "pnpm": ">= 8.7.0"}}