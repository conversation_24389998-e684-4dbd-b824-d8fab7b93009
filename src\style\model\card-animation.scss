@use "@/style/var/index" as *;

@keyframes fade-up {
  0% {
    opacity: 0;
    transform: translateY(60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 卡片过渡动画
// 根据遍历的卡片数量应用动画，卡片数量支持20个
@for $i from 0 through 20 {
  .animated-fade-up-#{$i} {
    opacity: 0; // 初始透明度
    animation-name: fade-up; // 应用动画
    animation-duration: 0.5s; // 动画持续时间
    animation-fill-mode: forwards; // 保持结束后的动画状态
    /* stylelint-disable-next-line scss/operator-no-unspaced */
    animation-delay: calc($i/10) + s; // 设置延迟-每次延迟0.1s
  }
}
