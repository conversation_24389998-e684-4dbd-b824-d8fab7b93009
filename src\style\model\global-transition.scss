/* global css transition */
@use "@/style/var/index" as *;

// Transition过渡动画

/* 轻过渡 */
// 进入
.fadeInOut-enter-active {
  animation: slide-enter-right 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes slide-enter-right {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-enter-right {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// 离开
.fadeInOut-leave-active {
  animation: slide-leave-right 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes slide-leave-right {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(20px);
  }
}

@keyframes slide-leave-right {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(20px);
  }
}

/* 卡片 */
// 进入
.cardInOut-enter-active {
  animation: swing-in-top-fwd 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
}

@keyframes swing-in-top-fwd {
  0% {
    opacity: 0;
    transform: rotateX(-100deg);
    transform-origin: top;
  }
  100% {
    opacity: 1;
    transform: rotateX(0deg);
    transform-origin: top;
  }
}

// 离开
.cardInOut-leave-active {
  animation: swing-out-top-bck 0.25s cubic-bezier(0.6, -0.28, 0.735, 0.045) both;
}

@keyframes swing-out-top-bck {
  0% {
    opacity: 1;
    transform: rotateX(0deg);
    transform-origin: top;
  }
  100% {
    opacity: 0;
    transform: rotateX(-100deg);
    transform-origin: top;
  }
}

/* 渐退 */
// 进入
.fadeOut-enter-active {
  animation: fade-in 0.25s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

// 离开
.fadeOut-leave-active {
  animation: fade-out 0.25s ease-out both;
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
