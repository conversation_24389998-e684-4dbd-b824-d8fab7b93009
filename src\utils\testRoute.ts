/**
 * @description: 测试多级菜单
 */
export const testMultilevelMenu = [
  {
    path: "/multilevel-menu/third-menu-3",
    name: "third-menu-3",
    component: () => import("@/views/multilevel-menu/third-menu/third-menu-3.vue"),
    meta: {
      title: "third-menu-3",
      link: "",
      hide: false,
      keepAlive: true,
      affix: false,
      iframe: false,
      roles: ["admin"],
      icon: "icon-menu",
      sort: 3
    }
  },
  {
    path: "/multilevel-menu/third-menu-4",
    name: "third-menu-4",
    component: () => import("@/views/multilevel-menu/third-menu/third-menu-4.vue"),
    meta: {
      title: "third-menu-4",
      link: "",
      hide: false,
      keepAlive: true,
      affix: false,
      iframe: false,
      roles: ["admin"],
      icon: "icon-menu",
      sort: 4
    }
  },
  {
    path: "/multilevel-menu/third-menu-5",
    name: "third-menu-5",
    component: () => import("@/views/multilevel-menu/third-menu/third-menu-5.vue"),
    meta: {
      title: "third-menu-5",
      link: "",
      hide: false,
      keepAlive: true,
      affix: false,
      iframe: false,
      roles: ["admin"],
      icon: "icon-menu",
      sort: 5
    }
  },
  {
    path: "/multilevel-menu/third-menu-6",
    name: "third-menu-6",
    component: () => import("@/views/multilevel-menu/third-menu/third-menu-6.vue"),
    meta: {
      title: "third-menu-6",
      link: "",
      hide: false,
      keepAlive: true,
      affix: false,
      iframe: false,
      roles: ["admin"],
      icon: "icon-menu",
      sort: 6
    }
  },
  {
    path: "/multilevel-menu/third-menu-7",
    name: "third-menu-7",
    component: () => import("@/views/multilevel-menu/third-menu/third-menu-7.vue"),
    meta: {
      title: "third-menu-7",
      link: "",
      hide: false,
      keepAlive: true,
      affix: false,
      iframe: false,
      roles: ["admin"],
      icon: "icon-menu",
      sort: 7
    }
  },
  {
    path: "/multilevel-menu/third-menu-8",
    name: "third-menu-8",
    component: () => import("@/views/multilevel-menu/third-menu/third-menu-8.vue"),
    meta: {
      title: "third-menu-8",
      link: "",
      hide: false,
      keepAlive: true,
      affix: false,
      iframe: false,
      roles: ["admin"],
      icon: "icon-menu",
      sort: 8
    }
  },
  {
    path: "/multilevel-menu/third-menu-9",
    name: "third-menu-9",
    component: () => import("@/views/multilevel-menu/third-menu/third-menu-9.vue"),
    meta: {
      title: "third-menu-9",
      link: "",
      hide: false,
      keepAlive: true,
      affix: false,
      iframe: false,
      roles: ["admin"],
      icon: "icon-menu",
      sort: 9
    }
  }
];
