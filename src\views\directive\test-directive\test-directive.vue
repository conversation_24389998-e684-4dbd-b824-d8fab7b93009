<template>
  <div class="snow-page">
    <div class="snow-inner">
      测试指令
      <!-- <button v-custom="{ goodsId, event: getFun }" :rowData="goodsId">文字变色</button> -->
      <!-- <button @click="jump">文字变色</button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
// const goodsId = ref(100);
// setTimeout(() => {
//   goodsId.value = 200;
// }, 1000);

// setTimeout(() => {
//   goodsId.value = 300;
// }, 3000);

// setTimeout(() => {
//   goodsId.value = 400;
// }, 4000);
// const getFun = (e: any) => {
//   console.log("点击事件", e);
// };
// const router = useRouter();
// const jump = () => {
//   router.push({
//     path: "/home",
//     query: {
//       name: "张三"
//     }
//   });
// };
</script>

<style lang="scss" scoped></style>
