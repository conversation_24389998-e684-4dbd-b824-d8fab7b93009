interface List {
  key: string;
  examId: string;
  examName: string;
  schoolId: string;
  schoolName: string;
  examTime: string;
  examType: string;
  savePath: string;
  createDate: string;
  creator: string;
  examStatus: string;
}

interface FormData {
  form: {
    examName: string;
    school: string;
    examType: string;
  };
  search: boolean;
}

interface RowSelection {
  type: string;
  showCheckedAll: boolean;
  onlyCurrent: boolean;
}

interface Pagination {
  showPageSize: boolean;
  showTotal: boolean;
  current: number;
  pageSize: number;
  total: number;
}

export type { List, FormData, RowSelection, Pagination };
