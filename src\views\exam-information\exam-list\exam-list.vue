<template>
  <div class="snow-page">
    <div class="snow-inner">
      <a-form ref="formRef" auto-label-width :model="formData.form">
        <a-row :gutter="16">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="examName" label="考试名称">
              <a-input v-model="formData.form.examName" placeholder="请输入考试名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="courseName" label="课程名称">
              <a-input v-model="formData.form.courseName" placeholder="请输入课程名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="school" label="学校">
              <a-select v-model="formData.form.school" placeholder="请选择学校" allow-clear>
                <a-option value="厦门大学附中">厦门大学附中</a-option>
                <a-option value="厦门一中">厦门一中</a-option>
                <a-option value="厦门双十中学">厦门双十中学</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="courseSubject" label="课程学科">
              <a-select v-model="formData.form.courseSubject" placeholder="请选择学科" allow-clear>
                <a-option value="语文">语文</a-option>
                <a-option value="数学">数学</a-option>
                <a-option value="英语">英语</a-option>
                <a-option value="物理">物理</a-option>
                <a-option value="化学">化学</a-option>
                <a-option value="生物">生物</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="examGrade" label="考试年级">
              <a-select v-model="formData.form.examGrade" placeholder="请选择年级" allow-clear>
                <a-option value="七年级">七年级</a-option>
                <a-option value="八年级">八年级</a-option>
                <a-option value="九年级">九年级</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="courseGrade" label="课程年级">
              <a-select v-model="formData.form.courseGrade" placeholder="请选择年级" allow-clear>
                <a-option value="七年级">七年级</a-option>
                <a-option value="八年级">八年级</a-option>
                <a-option value="九年级">九年级</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-space class="search-btn">
              <a-button type="primary" @click="getExamList">
                <template #icon>
                  <icon-search />
                </template>
                <template #default>查询</template>
              </a-button>
              <a-button @click="onReset">
                <template #icon>
                  <icon-refresh />
                </template>
                <template #default>重置</template>
              </a-button>
              <a-button type="primary" @click="handleAdd">
                <template #icon>
                  <icon-plus />
                </template>
                <template #default>新增</template>
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
      <a-table
        row-key="key"
        size="small"
        :bordered="{
          cell: true
        }"
        :scroll="{ x: '100%', y: '100%', minWidth: 1200 }"
        :loading="loading"
        :columns="columns"
        :data="data"
        :row-selection="rowSelection"
        v-model:selectedKeys="selectedKeys"
        :pagination="pagination"
        @page-change="pageChange"
        @page-size-change="pageSizeChange"
      >
        <template #examType="{ record }">
          <a-tag size="small" color="blue">{{ record.examType }}</a-tag>
        </template>
        <template #optional="{ record }">
          <a-space>
            <a-button size="mini" type="text" style="color: #1890ff">详情</a-button>
            <a-button size="mini" type="text" style="color: #1890ff">修改</a-button>
            <a-popconfirm content="确定删除这条数据吗?" type="warning">
              <a-button size="mini" type="text" style="color: #ff4d4f">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ExamList {
  key: string;
  examId: string;
  examName: string;
  schoolId: string;
  schoolName: string;
  examTime: string;
  examGrade: string;
  savePath: string;
  createDate: string;
  creator: string;
  examType: string;
  semester: string;
}

const formData = reactive({
  form: {
    examName: "",
    courseName: "",
    school: "",
    courseSubject: "",
    examGrade: "",
    courseGrade: ""
  }
});

const selectedKeys = ref<string[]>([]);
const rowSelection = reactive({
  type: "checkbox",
  showCheckedAll: true,
  onlyCurrent: false
});

const pagination = ref({
  showPageSize: true,
  showTotal: true,
  current: 1,
  pageSize: 10,
  total: 10
});

const pageChange = (page: number) => {
  pagination.value.current = page;
};

const pageSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize;
};

const columns = [
  {
    title: "考试编号",
    dataIndex: "examId",
    width: 120
  },
  {
    title: "考试名称",
    dataIndex: "examName",
    width: 150
  },
  {
    title: "学校编号",
    dataIndex: "schoolId",
    width: 100
  },
  {
    title: "学校名称",
    dataIndex: "schoolName",
    width: 120
  },
  {
    title: "考试时间",
    dataIndex: "examTime",
    width: 100
  },
  {
    title: "考试年级",
    dataIndex: "examGrade",
    width: 80
  },
  {
    title: "保存路径",
    dataIndex: "savePath",
    width: 150
  },
  {
    title: "创建日期",
    dataIndex: "createDate",
    width: 100
  },
  {
    title: "创建者",
    dataIndex: "creator",
    width: 80
  },
  {
    title: "考试类型",
    dataIndex: "examType",
    slotName: "examType",
    align: "center",
    width: 80
  },
  {
    title: "学期",
    dataIndex: "semester",
    width: 80
  },
  {
    title: "操作",
    slotName: "optional",
    align: "center",
    fixed: "right",
    width: 150
  }
];

const formRef = ref();
const onReset = () => {
  formRef.value.resetFields();
  getExamList();
};

const handleAdd = () => {
  // 新增考试信息的逻辑
  console.log("新增考试信息");
};

const loading = ref<boolean>(false);
const data = reactive<ExamList[]>([]);

const getExamList = async () => {
  try {
    loading.value = true;
    // 模拟数据，实际应该调用考试信息API
    const mockData = [
      {
        key: "1",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "2",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "3",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "4",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "5",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "6",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "7",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "8",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "9",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      },
      {
        key: "10",
        examId: "56789234",
        examName: "大同中学期末考试",
        schoolId: "35780",
        schoolName: "厦门大学附中",
        examTime: "2025-06-19",
        examGrade: "七年级",
        savePath: "D:\\厦门市语文",
        createDate: "已登录",
        creator: "admin",
        examType: "期末",
        semester: "上学期"
      }
    ];
    Object.assign(data, mockData);
    pagination.value.total = mockData.length;
  } finally {
    loading.value = false;
  }
};

getExamList();
</script>

<style lang="scss" scoped>
.snow-page {
  min-height: 100vh;
  padding: 16px;
  background-color: #f5f5f5;
}
.snow-inner {
  padding: 24px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}
.search-btn {
  margin-bottom: 20px;
  .arco-btn {
    margin-right: 8px;
  }
}
:deep(.arco-form-item) {
  margin-bottom: 16px;
}
:deep(.arco-table) {
  .arco-table-th {
    font-weight: 600;
    color: #262626;
    background-color: #fafafa;
  }
  .arco-table-td {
    border-bottom: 1px solid #f0f0f0;
  }
  .arco-table-tr:hover .arco-table-td {
    background-color: #f5f5f5;
  }
}
:deep(.arco-btn-text) {
  padding: 0 4px;
  &:hover {
    background-color: transparent;
  }
}
:deep(.arco-pagination) {
  justify-content: flex-end;
  margin-top: 16px;
}
</style>
