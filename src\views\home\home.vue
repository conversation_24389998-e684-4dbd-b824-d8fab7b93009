<template>
  <div class="snow-page">
    <div class="home-page">
      <!-- 主标题区域 -->
      <div class="header-section">
        <h1 class="main-title">数据赋能教与学 开启教育新时代</h1>
        <p class="subtitle">
          数据与AI技术深度融合，创新教育理论，推动教育全面发展。<br />
          为教师提供强大数字工具，为学习者打造个性化、高效学习环境。
        </p>
      </div>

      <!-- 功能模块区域 -->
      <div class="function-modules">
        <div class="module-grid">
          <div
            v-for="(module, index) in functionModules"
            :key="module.id"
            class="module-card"
            :class="`animated-fade-up-${index}`"
          >
            <div class="card-content">
              <div class="icon-container">
                <s-svg-icon :name="module.icon" :size="48" />
              </div>
              <h3 class="module-title">{{ module.name }}</h3>
              <p class="module-description">{{ module.description }}</p>
              <div class="card-arrow">
                <icon-right />
              </div>
            </div>
          </div>
          <div class="module-card special-card">
            <div class="card-header">
              <h3 class="module-title">作业测评</h3>
              <p class="module-description">实用简单明朗的测评功能，无需一次测试，提供批改功能</p>
            </div>
            <div class="side-links">
              <div class="link-item">
                <div class="link-icon">
                  <icon-mobile />
                  <span>手机扫描测评</span>
                  <icon-right />
                </div>
              </div>
              <div class="link-item">
                <div class="link-icon">
                  <icon-question-circle />
                  <span>问卷批测评</span><icon-right />
                </div>
              </div>
              <div class="link-item">
                <div class="link-icon">
                  <icon-question-circle />
                  <span>刷题器测评</span><icon-right />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 功能模块数据
const functionModules = ref([
  {
    id: 1,
    name: "智能阅卷",
    description: "采用智能阅卷的高效功能，无需一次测试，提供批改功能",
    icon: "data-analysis"
  },
  {
    id: 2,
    name: "考试阅卷",
    description: "简约支持功能，手机扫描测评功能，考试阅卷功能，高效简约",
    icon: "form"
  },
  {
    id: 3,
    name: "学情分析",
    description: "多维度、多维度、多角度、多形式的学情数据分析，让学习更高效",
    icon: "data"
  },
  {
    id: 4,
    name: "作业批改无人机",
    description: "机器智能批改功能，生成作文智能批改综合功能",
    icon: "functions"
  },
  {
    id: 5,
    name: "五段错题本",
    description: "学生可按日、周、月、期、年筛选错题，以便巩固复习",
    icon: "classify"
  },
  {
    id: 6,
    name: "选题组卷",
    description: "一键选题、智能组卷题，为教师们提供了强大的教学功能",
    icon: "table"
  },
  {
    id: 7,
    name: "基础信息设置",
    description: "班级信息、班级、科目、学生、教师等基础信息全面设置",
    icon: "set"
  }
]);
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  padding: 5px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  .header-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
    max-width: 1200px;
    padding: 20px 0;
    margin: auto;
    text-align: left;
    .main-title {
      margin-bottom: 24px;
      font-size: 36px;
      font-weight: 700;
      line-height: 1.3;
      color: #1a1a1a;
      letter-spacing: 1px;
    }
    .subtitle {
      max-width: 700px;
      margin: 0;
      font-size: 18px;
      font-weight: 400;
      line-height: 1.8;
      color: #555555;
    }
  }
  .function-modules {
    margin-bottom: 40px;
    .module-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(190px, 1fr));
      gap: 30px;
      max-width: 1200px;
      margin: 0 auto;
    }
    .module-card {
      position: relative;
      padding: 14px;
      overflow: hidden;
      background: transparent;
      border: 2px solid rgb(74 144 226 / 30%);
      border-radius: 16px;
      box-shadow: 0 4px 20px rgb(0 0 0 / 8%);
      transition: all 0.3s ease;
      &:hover {
        border-color: #4a90e2;
        box-shadow: 0 8px 30px rgb(0 0 0 / 12%);
        transform: translateY(-4px);
      }
      &.special-card {
        background: transparent;
        border-color: rgb(74 144 226 / 30%);
        .card-header {
          margin-bottom: 20px;
          .module-title {
            margin-bottom: 12px;
            font-size: 20px;
            font-weight: 700;
            color: #1a1a1a;
          }
          .module-description {
            margin-bottom: 0;
            font-size: 14px;
            line-height: 1.6;
            color: #666666;
          }
        }
      }
      .card-content {
        position: relative;
        z-index: 2;
        .icon-container {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80px;
          height: 80px;
          margin: 0 auto 16px;
          color: white;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 20px;
        }
        .module-title {
          margin-bottom: 8px;
          font-size: 18px;
          font-weight: 600;
          color: #1a1a1a;
          text-align: center;
        }
        .module-description {
          margin-bottom: 20px;
          font-size: 12px;
          line-height: 1.5;
          color: #666666;
          text-align: center;
        }
        .card-arrow {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          margin: 0 auto;
          color: #666666;
          background: #f0f2f5;
          border-radius: 50%;
          transition: all 0.3s ease;
        }
        .side-links {
          padding: 16px;
          margin-top: 20px;
          .link-item {
            position: relative;
            display: flex !important;
            flex-flow: row nowrap !important;
            align-items: center !important;
            padding: 20px 18px;
            margin: 12px 8px;
            overflow: hidden;
            cursor: pointer;
            background: transparent;
            border: 1px solid #e1ecf7;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgb(74 144 226 / 8%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            &::before {
              position: absolute;
              top: 0;
              left: 0;
              width: 3px;
              height: 100%;
              content: "";
              background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
              transform: scaleY(0);
              transition: transform 0.3s ease;
            }
            &:last-child {
              margin-bottom: 12px;
            }
            &:hover {
              background: rgb(255 255 255 / 60%);
              border-color: #4a90e2;
              box-shadow: 0 4px 16px rgb(74 144 226 / 15%);
              transform: translateX(6px) translateY(-2px);
              &::before {
                transform: scaleY(1);
              }
              .link-icon {
                color: white;
                background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
                box-shadow: 0 4px 12px rgb(74 144 226 / 30%);
                transform: scale(1.1);
              }
              .link-arrow {
                color: #4a90e2;
                transform: translateX(4px);
              }
              span {
                color: #2c5aa0;
              }
            }
            &:active {
              transform: translateX(4px) translateY(-1px);
            }
            .link-icon {
              display: flex !important;
              flex: 0 0 auto !important;
              align-items: center !important;
              justify-content: center !important;
              width: 28px !important;
              height: 28px !important;
              margin-right: 14px;
              color: #4a90e2;
              background: linear-gradient(135deg, #e8f0fe 0%, #d1e7fd 100%);
              border-radius: 8px;
              box-shadow: 0 2px 4px rgb(74 144 226 / 10%);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              .arco-icon {
                font-size: 15px;
                line-height: 1;
                transition: transform 0.3s ease;
              }
            }
            span {
              display: block !important;
              flex: 1 1 auto !important;
              font-size: 14px;
              font-weight: 600;
              line-height: 28px !important;
              color: #2d3748;
              letter-spacing: 0.2px;
              white-space: nowrap;
              transition: color 0.3s ease;
            }
            .link-arrow {
              display: flex !important;
              flex: 0 0 auto !important;
              align-items: center !important;
              justify-content: center !important;
              width: 24px !important;
              height: 24px !important;
              margin-left: 8px;
              color: #a0aec0;
              background: rgb(74 144 226 / 5%);
              border-radius: 6px;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              .arco-icon {
                font-size: 13px;
                line-height: 1;
                transition: transform 0.3s ease;
              }
            }
          }
        }
      }
      &:hover .card-arrow {
        color: white;
        background: #1890ff;
      }
    }
  }
}

// 动画效果
@for $i from 0 through 10 {
  .animated-fade-up-#{$i} {
    animation: fade-in-up 0.6s ease-out #{$i * 0.1}s both;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (width <= 768px) {
  .home-page {
    padding: 16px;
    .header-section {
      padding: 20px 10px;
      margin: 40px auto;
      text-align: left;
      .main-title {
        margin-bottom: 16px;
        font-size: 28px;
        letter-spacing: 0.5px;
      }
      .subtitle {
        max-width: 90%;
        font-size: 16px;
      }
    }
    .function-modules {
      .module-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
      .module-card {
        padding: 20px;
      }
    }
  }
}
</style>
