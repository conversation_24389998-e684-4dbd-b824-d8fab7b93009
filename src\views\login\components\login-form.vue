<template>
  <div>
    <div class="login_form_box">
      <a-form :rules="rules" :model="form" layout="vertical" @submit="onSubmit" class="login-form">
        <a-form-item field="username" :hide-asterisk="true">
          <a-input v-model="form.username" allow-clear placeholder="请输入用户名" size="large">
            <template #prefix>
              <icon-user />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item field="password" :hide-asterisk="true">
          <a-input-password v-model="form.password" allow-clear placeholder="请输入密码" size="large">
            <template #prefix>
              <icon-lock />
            </template>
          </a-input-password>
        </a-form-item>
        <a-form-item field="verifyCode" :hide-asterisk="true">
          <div class="verifyCode">
            <a-input style="width: 200px" v-model="form.verifyCode" allow-clear placeholder="请输入验证码" size="large" />
            <s-verify-code :content-height="40" :font-size-max="30" :content-width="120" @verify-code-change="verifyCodeChange" />
          </div>
        </a-form-item>
        <a-form-item field="remember">
          <div class="remember">
            <a-checkbox v-model="form.remember">记住密码</a-checkbox>
            <div class="forgot-password">忘记密码</div>
          </div>
        </a-form-item>
        <a-form-item>
          <a-button long type="primary" html-type="submit" size="large" class="login-btn">登录</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { useUserInfoStore } from "@/store/modules/user-info";
import { loginAPI } from "@/api/modules/user/index";
import { useRouteConfigStore } from "@/store/modules/route-config";
import { useSystemStore } from "@/store/modules/system";
let userStores = useUserInfoStore();
const routeStore = useRouteConfigStore();
const router = useRouter();
const form = ref({
  username: "admin",
  password: "123456",
  verifyCode: null,
  remember: false
});
const rules = ref({
  username: [
    {
      required: true,
      message: "请输入账号"
    }
  ],
  password: [
    {
      required: true,
      message: "请输入密码"
    }
  ],
  verifyCode: [
    {
      required: true,
      message: "请输入验证码"
    },
    {
      validator: (value: string, cb: any) => {
        if (value !== verifyCode.value) {
          cb("请输入正确的验证码");
        } else {
          cb();
        }
      }
    }
  ]
});
const verifyCode = ref("");
const verifyCodeChange = (code: string) => (verifyCode.value = code);

// 提交表单
const onSubmit = async ({ errors }: any) => {
  if (errors) return;
  onLogin();
};

// 登录
const onLogin = async () => {
  // 登录
  let res = await loginAPI(form.value);
  // 存储token
  await userStores.setToken(res.data.token);
  // 加载用户信息
  await userStores.setAccount();
  // 加载路由信息
  await routeStore.initSetRouter();

  arcoMessage("success", "登录成功");
  // 跳转首页
  router.replace("/home");
  // 设置字典
  useSystemStore().setDictData();
};
</script>

<style lang="scss" scoped>
.login_form_box {
  padding: 0 10px;
  margin-top: 30px;
  .login-form {
    :deep(.arco-form-item) {
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    :deep(.arco-input-wrapper) {
      height: 48px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      &:hover {
        background: #ffffff;
        border-color: #4080ff;
      }
      &:focus-within {
        background: #ffffff;
        border-color: #4080ff;
        box-shadow: 0 0 0 2px rgb(64 128 255 / 10%);
      }
    }
    :deep(.arco-input) {
      font-size: 14px;
      background: transparent;
      &::placeholder {
        color: #9ca3af;
      }
    }
    :deep(.arco-input-password) {
      background: transparent;
    }
    :deep(.arco-checkbox) {
      .arco-checkbox-icon {
        border-radius: 4px;
      }
    }
  }
  .verifyCode {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .remember {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin: 0 0 8px;
    font-size: 14px;
    .forgot-password {
      color: #4080ff;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .login-btn {
    height: 48px;
    margin-top: 0;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #4080ff 0%, #1890ff 100%);
    border: none;
    border-radius: 6px;
    &:hover {
      background: linear-gradient(135deg, #1890ff 0%, #4080ff 100%);
      box-shadow: 0 4px 12px rgb(64 128 255 / 30%);
      transform: translateY(-1px);
    }
    &:active {
      transform: translateY(0);
    }
  }
}
</style>
