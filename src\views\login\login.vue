<template>
  <div class="container">
    <div class="login">
      <LoginBanner v-if="isPc" />
      <div class="login_box">
        <div class="login_title">欢迎登录</div>
        <LoginForm />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import LoginBanner from "@/views/login/components/login-banner.vue";
import LoginForm from "@/views/login/components/login-form.vue";
import { useDevicesSize } from "@/hooks/useDevicesSize";

const { isPc } = useDevicesSize();
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  .login {
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    align-items: center;
    max-width: 1000px;
    height: 500px;
    background: rgb(255 255 255 / 95%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgb(0 0 0 / 10%);
    backdrop-filter: blur(10px);
    transform: translate(-50%, -50%);
    .login_box {
      position: relative;
      box-sizing: border-box;
      width: 380px;
      height: 100%;
      padding: 50px 35px 35px;
      background: #ffffff;
      border-radius: 0 20px 20px 0;
      box-shadow: inset 0 0 0 1px rgb(0 0 0 / 5%);
      .login_title {
        margin-bottom: 35px;
        font-size: 26px;
        font-weight: 600;
        color: #2c3e50;
        text-align: center;
        letter-spacing: 0.5px;
      }
    }
  }
}
</style>
